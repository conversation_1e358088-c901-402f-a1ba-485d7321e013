class_name UpgradeUI
extends Control

@export var meta_currency_label: Label
@export var upgrade_list_container: VBoxContainer
@export var upgrade_panel_scene: PackedScene
@export var close_button: Button

var _upgrade_panels: Array[StatUpgradePanel] = []

func _ready() -> void:
	GameProgress.progress_changed.connect(_update_upgrade_ui)
	close_button.pressed.connect(_on_close_button_pressed)
	_build_upgrade_ui()

func show_ui() -> void:
	visible = true
	_update_upgrade_ui()
	GameStateService.block_gameplay_input()

func hide_ui() -> void:
	visible = false
	GameStateService.enable_gameplay_input()

func _update_meta_currency_display() -> void:
	var currency: int = GameProgress.get_meta_currency()
	meta_currency_label.text = "Мета-валюта: " + str(currency)

func _build_upgrade_ui() -> void:
	if not upgrade_list_container or not upgrade_panel_scene:
		return

	for stat_data in GameProgress.get_upgradeable_stats():
		var panel_instance: StatUpgradePanel = upgrade_panel_scene.instantiate()
		upgrade_list_container.add_child(panel_instance)
		panel_instance.setup(stat_data)
		panel_instance.upgrade_requested.connect(_on_stat_upgrade_requested)
		_upgrade_panels.append(panel_instance)

func _update_upgrade_ui() -> void:
	_update_meta_currency_display()

	var player_currency: int = GameProgress.get_meta_currency()

	for panel in _upgrade_panels:
		if panel._stat_data:
			var current_level: int = GameProgress.get_stat_upgrade_level(panel._stat_data.stat_id)
			var cost: int = GameProgress.get_stat_upgrade_cost(panel._stat_data.stat_id)
			panel.update_view(current_level, cost, player_currency)

func _on_stat_upgrade_requested(stat_id: StringName) -> void:
	if GameProgress.upgrade_stat(stat_id):
		print("Улучшен стат: ", stat_id)

func _on_close_button_pressed() -> void:
	hide_ui()
