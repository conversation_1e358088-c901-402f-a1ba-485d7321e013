[gd_scene load_steps=4 format=3 uid="uid://bjcku4dtsyta2"]

[ext_resource type="Script" uid="uid://cahb1bubw07hp" path="res://src/hub/upgrade_npc.gd" id="1_upgrade_npc"]
[ext_resource type="Texture2D" uid="uid://dunym6c7vtv8i" path="res://assets/cog_silver.png" id="2_cog_silver"]

[sub_resource type="CircleShape2D" id="CircleShape2D_qf2dy"]
radius = 3.0

[node name="UpgradeNpc" type="Area2D"]
collision_layer = 0
script = ExtResource("1_upgrade_npc")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("2_cog_silver")

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
shape = SubResource("CircleShape2D_qf2dy")
