[gd_scene load_steps=2 format=3 uid="uid://bc8djghux7vga"]

[ext_resource type="Script" uid="uid://qaqc0q7nom5h" path="res://src/hub/stat_upgrade_panel.gd" id="1_stat_upgrade_panel"]

[node name="StatUpgradePanel" type="VBoxContainer" node_paths=PackedStringArray("stat_name_label", "stat_description_label", "current_level_label", "cost_label", "upgrade_button")]
script = ExtResource("1_stat_upgrade_panel")
stat_name_label = NodePath("StatNameLabel")
stat_description_label = NodePath("StatDescriptionLabel")
current_level_label = NodePath("InfoContainer/CurrentLevelLabel")
cost_label = NodePath("InfoContainer/CostLabel")
upgrade_button = NodePath("UpgradeButton")

[node name="StatNameLabel" type="Label" parent="."]
layout_mode = 2
theme_override_font_sizes/font_size = 50
text = "Название стата"
horizontal_alignment = 1

[node name="StatDescriptionLabel" type="Label" parent="."]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Описание стата"
autowrap_mode = 2

[node name="InfoContainer" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="CurrentLevelLabel" type="Label" parent="InfoContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 30
text = "Уровень: 0"

[node name="CostLabel" type="Label" parent="InfoContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 30
text = "Стоимость: 10"
horizontal_alignment = 2

[node name="UpgradeButton" type="Button" parent="."]
layout_mode = 2
theme_override_font_sizes/font_size = 40
text = "Улучшить"
