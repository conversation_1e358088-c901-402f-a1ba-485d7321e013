class_name UpgradeNpc
extends Area2D

@export var upgrade_ui: UpgradeUI

func _ready() -> void:
	body_entered.connect(_on_area_2d_body_entered)
	body_exited.connect(_on_area_2d_body_exited)

func _on_area_2d_body_entered(body: Node2D) -> void:
	if body.is_in_group(&"player"):
		upgrade_ui.show_ui()

func _on_area_2d_body_exited(body: Node2D) -> void:
	if body.is_in_group(&"player"):
		upgrade_ui.hide_ui()
